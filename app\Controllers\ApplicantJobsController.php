<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Services\ViewTrackingService;

class ApplicantJobsController extends BaseController
{
    protected $session;
    protected $viewTrackingService;
    protected $positionsModel;
    protected $organizationsModel;
    protected $exercisesModel;
    protected $applicationsModel;
    protected $appxApplicationFilesModel;
    protected $applicantExperiencesModel;
    protected $appxApplicationExperiencesModel;
    protected $applicantEducationModel;
    protected $appxApplicationEducationModel;
    protected $applicantFilesModel;

    public function __construct()
    {
        helper(['form', 'url', 'application']);
        $this->session = session();

        // Initialize models
        $this->positionsModel = new \App\Models\PositionsModel();
        $this->organizationsModel = new \App\Models\DakoiiOrgModel();
        $this->exercisesModel = new \App\Models\ExerciseModel();
        $this->applicationsModel = new \App\Models\AppxApplicationDetailsModel();
        $this->appxApplicationFilesModel = new \App\Models\AppxApplicationFilesModel();
        $this->applicantExperiencesModel = new \App\Models\ApplicantsExperiencesModel();
        $this->appxApplicationExperiencesModel = new \App\Models\AppxApplicationExperiencesModel();
        $this->applicantEducationModel = new \App\Models\ApplicantEducationModel();
        $this->appxApplicationEducationModel = new \App\Models\AppxApplicationEducationModel();
        $this->applicantFilesModel = new \App\Models\ApplicantFilesModel();
        $this->viewTrackingService = new ViewTrackingService();
    }

    /**
     * Validate and ensure exercise_id and org_id are present
     *
     * @param array $applicationData Reference to application data array
     * @param int $positionId Position ID to retrieve data from
     * @return bool True if validation passes, false otherwise
     */
    private function validateApplicationData(&$applicationData, $positionId)
    {
        // If exercise_id is empty, get it from PositionsModel
        if (empty($applicationData['exercise_id'])) {
            $position = $this->positionsModel->select('exercise_id, org_id')->find($positionId);
            if ($position && !empty($position['exercise_id'])) {
                $applicationData['exercise_id'] = $position['exercise_id'];
            } else {
                log_message('error', 'Exercise ID not found for position ID: ' . $positionId);
                return false;
            }
        }

        // If org_id is empty, get it from PositionsModel
        if (empty($applicationData['org_id'])) {
            if (!isset($position)) {
                $position = $this->positionsModel->select('exercise_id, org_id')->find($positionId);
            }
            if ($position && !empty($position['org_id'])) {
                $applicationData['org_id'] = $position['org_id'];
            } else {
                log_message('error', 'Organization ID not found for position ID: ' . $positionId);
                return false;
            }
        }

        return true;
    }

    public function index()
    {
        try {
            // Get applicant ID from session
            $applicant_id = session()->get('applicant_id');

            if (!$applicant_id) {
                return redirect()->to('applicant/login')->with('error', 'Please login to view job openings');
            }

            // Get filter parameters
            $selectedOrgId = $this->request->getGet('org_id');
            $selectedPositionGroup = $this->request->getGet('position_group');
            $selectedAdvertisementType = $this->request->getGet('advertisement_type');
            $searchTerm = $this->request->getGet('search');

            // Load models
            $exerciseModel = new \App\Models\ExerciseModel();
            $orgModel = new \App\Models\DakoiiOrgModel();
            $positionsModel = new \App\Models\PositionsModel();
            $applicantsModel = new \App\Models\ApplicantsModel();
            $positionsGroupModel = new \App\Models\PositionsGroupModel();

            // Get current applicant's organization information
            $applicant = $applicantsModel->find($applicant_id);
            $applicantOrgId = $applicant['employee_of_org_id'] ?? null;

            // Get all organizations for filter dropdown
            $organizations = $orgModel->where('is_active', 1)->findAll();

            // Get all position groups for filter dropdown
            $positionGroups = $positionsGroupModel
                ->select('positions_groups.*, exercises.is_internal, exercises.org_id')
                ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
                ->where('exercises.status', 'published')
                ->orderBy('positions_groups.group_name', 'ASC')
                ->findAll();

            // Filter position groups based on applicant's access
            $accessiblePositionGroups = [];
            foreach ($positionGroups as $group) {
                if ($group['is_internal'] == 0) {
                    // External position group - accessible to all
                    $accessiblePositionGroups[] = $group;
                } elseif ($applicantOrgId && $group['org_id'] == $applicantOrgId) {
                    // Internal position group - accessible only to employees of the organization
                    $accessiblePositionGroups[] = $group;
                }
            }

            // Get positions directly with exercise and organization data
            $positionsQuery = $positionsModel
                ->select('
                    positions.*,
                    positions_groups.group_name as position_group_name,
                    exercises.id as exercise_id,
                    exercises.exercise_name,
                    exercises.advertisement_no,
                    exercises.gazzetted_no,
                    exercises.publish_date_from,
                    exercises.publish_date_to,
                    exercises.is_internal,
                    exercises.org_id,
                    dakoii_org.org_name,
                    dakoii_org.org_code,
                    dakoii_org.location_lock_province,
                    dakoii_org.logo_path
                ')
                ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
                ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
                ->join('dakoii_org', 'exercises.org_id = dakoii_org.id', 'left')
                ->where('positions.status', 'active')
                ->where('exercises.status', 'published')
                ->where('dakoii_org.is_active', 1);

            // Apply internal/external filtering logic
            if ($applicantOrgId) {
                // Applicant is an employee of an organization
                // Show external positions + internal positions from their organization
                $positionsQuery->groupStart()
                    ->where('exercises.is_internal', 0) // External positions (visible to all)
                    ->orGroupStart()
                        ->where('exercises.is_internal', 1) // Internal positions
                        ->where('exercises.org_id', $applicantOrgId) // Only from applicant's organization
                    ->groupEnd()
                ->groupEnd();
            } else {
                // Applicant is not an employee of any organization
                // Show only external positions
                $positionsQuery->where('exercises.is_internal', 0);
            }

            // Apply organization filter
            if ($selectedOrgId) {
                $positionsQuery->where('exercises.org_id', $selectedOrgId);
            }

            // Apply position group filter
            if ($selectedPositionGroup) {
                $positionsQuery->where('positions_groups.id', $selectedPositionGroup);
            }

            // Apply advertisement type filter
            if ($selectedAdvertisementType !== null && $selectedAdvertisementType !== '') {
                $positionsQuery->where('exercises.is_internal', $selectedAdvertisementType);
            }

            // Apply search filter
            if ($searchTerm) {
                $positionsQuery->groupStart()
                    ->like('positions.designation', $searchTerm)
                    ->orLike('positions.position_reference', $searchTerm)
                    ->orLike('positions.classification', $searchTerm)
                    ->orLike('positions.location', $searchTerm)
                    ->orLike('positions.award', $searchTerm)
                    ->orLike('positions_groups.group_name', $searchTerm)
                    ->orLike('exercises.exercise_name', $searchTerm)
                    ->orLike('exercises.advertisement_no', $searchTerm)
                    ->orLike('exercises.gazzetted_no', $searchTerm)
                    ->orLike('dakoii_org.org_name', $searchTerm)
                ->groupEnd();
            }

            // Order by publish date (newest first), then by designation
            $positionsQuery->orderBy('exercises.publish_date_from', 'DESC')
                          ->orderBy('positions.designation', 'ASC');

            $allPositions = $positionsQuery->findAll();

            // Check which positions the applicant has already applied for
            $appliedPositionIds = [];
            $hasUploadedFiles = false;
            if ($applicant_id) {
                $applicationsModel = new \App\Models\AppxApplicationDetailsModel();
                $appliedApplications = $applicationsModel
                    ->select('position_id')
                    ->where('applicant_id', $applicant_id)
                    ->findAll();

                $appliedPositionIds = array_column($appliedApplications, 'position_id');

                // Check if applicant has uploaded at least one file
                $applicantFilesModel = new \App\Models\ApplicantFilesModel();
                $uploadedFiles = $applicantFilesModel->where('applicant_id', $applicant_id)->findAll();
                $hasUploadedFiles = !empty($uploadedFiles);
            }

            // Format position data for the view
            $allJobData = [];
            foreach ($allPositions as $position) {
                $allJobData[] = [
                    'position' => $position,
                    'exercise' => [
                        'id' => $position['exercise_id'],
                        'exercise_name' => $position['exercise_name'],
                        'advertisement_no' => $position['advertisement_no'],
                        'gazzetted_no' => $position['gazzetted_no'],
                        'publish_date_from' => $position['publish_date_from'],
                        'publish_date_to' => $position['publish_date_to'],
                        'is_internal' => $position['is_internal'],
                        'org_id' => $position['org_id']
                    ],
                    'organization' => [
                        'id' => $position['org_id'],
                        'org_name' => $position['org_name'],
                        'org_code' => $position['org_code'],
                        'location_lock_province' => $position['location_lock_province'],
                        'logo_path' => $position['logo_path']
                    ],
                    'has_applied' => in_array($position['id'], $appliedPositionIds),
                    'can_apply' => $hasUploadedFiles
                ];
            }

            return view('applicant/applicant_job_openings', [
                'title' => 'Job Openings',
                'menu' => 'jobs',
                'jobData' => $allJobData,
                'organizations' => $organizations,
                'positionGroups' => $accessiblePositionGroups,
                'selectedOrgId' => $selectedOrgId,
                'selectedPositionGroup' => $selectedPositionGroup,
                'selectedAdvertisementType' => $selectedAdvertisementType,
                'searchTerm' => $searchTerm,
                'totalJobs' => count($allJobData),
                'filteredJobs' => count($allJobData),
                'hasUploadedFiles' => $hasUploadedFiles
            ]);

        } catch (\Exception $e) {
            // Log the error
            log_message('error', 'Error loading applicant jobs: ' . $e->getMessage());

            return view('applicant/applicant_job_openings', [
                'title' => 'Job Openings',
                'menu' => 'jobs',
                'jobData' => [],
                'organizations' => [],
                'selectedOrgId' => $selectedOrgId,
                'searchTerm' => $searchTerm,
                'totalJobs' => 0,
                'filteredJobs' => 0,
                'error' => 'Unable to load job openings. Please try again later.'
            ]);
        }
    }

    public function view($exerciseId)
    {
        try {
            // Get applicant ID from session
            $applicant_id = session()->get('applicant_id');

            if (!$applicant_id) {
                return redirect()->to('applicant/login')->with('error', 'Please login to view exercise details');
            }

            // Load models
            $exerciseModel = new \App\Models\ExerciseModel();
            $orgModel = new \App\Models\DakoiiOrgModel();
            $positionsGroupModel = new \App\Models\PositionsGroupModel();
            $positionsModel = new \App\Models\PositionsModel();
            $applicationsModel = new \App\Models\AppxApplicationDetailsModel();
            $applicantsModel = new \App\Models\ApplicantsModel();

            // Get current applicant's organization information
            $applicant = $applicantsModel->find($applicant_id);
            $applicantOrgId = $applicant['employee_of_org_id'] ?? null;

            // Get exercise from database
            $exercise = $exerciseModel
                ->where('id', $exerciseId)
                ->where('status', 'published')
                ->first();

            if (!$exercise) {
                return redirect()->to('/applicant/jobs')->with('error', 'Exercise not found or not published.');
            }

            // Check if applicant has access to this exercise based on internal/external rules
            if ($exercise['is_internal'] == 1) {
                // This is an internal exercise
                if (!$applicantOrgId || $applicantOrgId != $exercise['org_id']) {
                    // Applicant is not an employee of the organization that posted this internal exercise
                    return redirect()->to('/applicant/jobs')->with('error', 'Access denied. This is an internal position for employees of ' . $exercise['org_name'] . ' only.');
                }
            }
            // External exercises (is_internal = 0) are accessible to all applicants

            // Get organization from database
            $organization = $orgModel->find($exercise['org_id']);
            if (!$organization) {
                return redirect()->to('/applicant/jobs')->with('error', 'Organization not found.');
            }

            // Get position groups for this exercise
            $positionGroups = $positionsGroupModel
                ->where('exercise_id', $exerciseId)
                ->orderBy('group_name', 'ASC')
                ->findAll();

            // Get all positions for this exercise with related data
            $allPositions = $positionsModel
                ->select('
                    positions.*,
                    positions_groups.group_name as position_group_name
                ')
                ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
                ->where('positions_groups.exercise_id', $exerciseId)
                ->where('positions.status', 'active')
                ->orderBy('positions_groups.group_name', 'ASC')
                ->orderBy('positions.designation', 'ASC')
                ->findAll();

            // Get applied positions for current applicant (if logged in)
            $appliedPositionIds = [];
            if (session()->get('applicant_id')) {
                $appliedPositions = $applicationsModel
                    ->select('position_id')
                    ->where('applicant_id', session()->get('applicant_id'))
                    ->findAll();
                $appliedPositionIds = array_column($appliedPositions, 'position_id');
            }

            // Group positions by their groups
            $groupedPositions = [];
            foreach ($positionGroups as $group) {
                $groupPositions = array_filter($allPositions, function($pos) use ($group) {
                    return $pos['position_group_id'] == $group['id'];
                });

                // Mark applied positions and add has_applied flag
                foreach ($groupPositions as &$position) {
                    $position['has_applied'] = in_array($position['id'], $appliedPositionIds);
                }

                if (!empty($groupPositions)) {
                    $groupedPositions[$group['group_name']] = array_values($groupPositions);
                }
            }

            return view('applicant/applicant_exercise_details', [
                'title' => 'Exercise Details - ' . $exercise['exercise_name'],
                'menu' => 'jobs',
                'exercise' => $exercise,
                'organization' => $organization,
                'positions' => $groupedPositions
            ]);

        } catch (\Exception $e) {
            // Log the error
            log_message('error', 'Error loading exercise details: ' . $e->getMessage());

            return redirect()->to('/applicant/jobs')->with('error', 'Unable to load exercise details. Please try again later.');
        }
    }

    public function position($positionId)
    {
        // Get applicant ID from session
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to view position details');
        }

        // Get position details
        $position = $this->positionsModel
            ->where('id', $positionId)
            ->where('status', 'active')
            ->first();

        if (!$position) {
            return redirect()->to('/applicant/jobs')->with('error', 'Position not found or not active.');
        }

        // Get organization details
        $orgData = $this->organizationsModel->find($position['org_id']);
        if (!$orgData) {
            return redirect()->to('/applicant/jobs')->with('error', 'Organization not found.');
        }

        // Map organization data to expected field names for the view
        $organization = [
            'id' => $orgData['id'],
            'name' => $orgData['org_name'],
            'org_name' => $orgData['org_name'],
            'org_code' => $orgData['org_code'],
            'orglogo' => $orgData['logo_path'],
            'logo_path' => $orgData['logo_path'],
            'addlockprov' => $orgData['location_lock_province'],
            'location_lock_province' => $orgData['location_lock_province'],
            'description' => $orgData['description'] ?? '',
            'postal_address' => $orgData['postal_address'] ?? '',
            'phone_numbers' => $orgData['phone_numbers'] ?? '',
            'email_addresses' => $orgData['email_addresses'] ?? '',
            'is_active' => $orgData['is_active'] ?? 1,
            'is_locationlocked' => $orgData['is_locationlocked'] ?? 0
        ];

        // Get exercise details
        $exercise = $this->exercisesModel
            ->where('id', $position['exercise_id'])
            ->where('status', 'published')
            ->first();

        if (!$exercise) {
            return redirect()->to('/applicant/jobs')->with('error', 'Exercise not found or not published.');
        }

        // Get applicant details
        $applicantModel = new \App\Models\ApplicantsModel();
        $applicant = $applicantModel->find(session()->get('applicant_id'));
        $applicantOrgId = $applicant['employee_of_org_id'] ?? null;

        // Check if applicant has access to this position based on internal/external rules
        if ($exercise['is_internal'] == 1) {
            // This is an internal position
            if (!$applicantOrgId || $applicantOrgId != $exercise['org_id']) {
                // Applicant is not an employee of the organization that posted this internal position
                return redirect()->to('/applicant/jobs')->with('error', 'Access denied. This is an internal position for employees of ' . $organization['org_name'] . ' only.');
            }
        }
        // External positions (is_internal = 0) are accessible to all applicants

        // Check if applicant has already applied for this position
        $existingApplication = $this->applicationsModel
            ->where('applicant_id', session()->get('applicant_id'))
            ->where('position_id', $positionId)
            ->first();

        // Track position view
        $this->viewTrackingService->trackView($positionId);

        return view('applicant/applicant_position_details', [
            'title' => $position['designation'],
            'menu' => 'jobs',
            'position' => $position,
            'organization' => $organization,
            'exercise' => $exercise,
            'applicant' => $applicant,
            'has_applied' => !empty($existingApplication),
            'application' => $existingApplication
        ]);
    }

    public function submissionInterface($positionId)
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        // Load models
        $exerciseModel = new \App\Models\ExerciseModel();
        $orgModel = new \App\Models\DakoiiOrgModel();
        $positionsModel = new \App\Models\PositionsModel();
        $applicationsModel = new \App\Models\AppxApplicationDetailsModel();
        $applicantFilesModel = new \App\Models\ApplicantFilesModel();

        // Check if applicant has uploaded at least one file
        $uploadedFiles = $applicantFilesModel->where('applicant_id', $applicant_id)->findAll();
        if (empty($uploadedFiles)) {
            return redirect()->to('applicant/profile/files/create')
                ->with('error', 'You must upload at least one supporting document before applying for positions. Please upload your necessary application documents first.');
        }

        // Check profile completion
        $applicantsModel = new \App\Models\ApplicantsModel();
        $applicant = $applicantsModel->find($applicant_id);
        $required_fields = ['first_name', 'last_name', 'gender', 'dobirth', 'contact_details', 'location_address', 'place_of_origin', 'citizenship', 'current_employer', 'current_position', 'current_salary', 'is_public_servant'];

        $missing_fields = [];
        foreach ($required_fields as $field) {
            // Special handling for is_public_servant field (tinyint with 0/1 values)
            if ($field === 'is_public_servant') {
                // Consider it filled if it's set to 0 or 1 (not null/empty)
                if (!isset($applicant[$field]) || ($applicant[$field] !== '0' && $applicant[$field] !== '1' && $applicant[$field] !== 0 && $applicant[$field] !== 1)) {
                    $missing_fields[] = $field;
                }
            } else {
                // Regular field validation
                if (empty($applicant[$field])) {
                    $missing_fields[] = $field;
                }
            }
        }

        if (!empty($missing_fields)) {
            return redirect()->to('applicant/profile')
                ->with('error', 'Please complete your profile before applying. Missing required information: ' . implode(', ', $missing_fields));
        }

        // Get position details
        $position = $positionsModel->find($positionId);
        if (!$position) {
            return redirect()->to('/applicant/jobs')->with('error', 'Position not found.');
        }

        // Get exercise details
        $exercise = $exerciseModel->find($position['exercise_id']);
        if (!$exercise || (isset($exercise['status']) && $exercise['status'] !== 'published')) {
            return redirect()->to('/applicant/jobs')->with('error', 'Exercise not found or not published.');
        }

        // Get organization details
        $organization = $orgModel->find($position['org_id']);

        // Check if already applied
        $existingApplication = $applicationsModel
            ->where('applicant_id', $applicant_id)
            ->where('position_id', $positionId)
            ->first();

        if ($existingApplication) {
            return redirect()->to('/applicant/applications')->with('error', 'You have already applied for this position.');
        }

        return view('applicant/applicant_application_submission', [
            'title' => 'Application Submission - ' . $position['designation'],
            'menu' => 'jobs',
            'position' => $position,
            'organization' => $organization,
            'exercise' => $exercise
        ]);
    }

    public function processApplication($positionId)
    {
        try {
            if (!$this->request->isAJAX()) {
                return $this->response->setJSON(['success' => false, 'message' => 'Invalid request method']);
            }

            $applicant_id = session()->get('applicant_id');

            if (!$applicant_id) {
                return $this->response->setJSON(['success' => false, 'message' => 'Please login to continue']);
            }

            // STEP 1: Pre-requisite - Upload all local files to GCS and delete local files
            log_message('info', 'STEP 1: Starting pre-requisite file processing during profile generation');

            // Initialize GCS service
            $gcsService = null;
            try {
                $gcsService = new \App\Services\GoogleCloudStorageService();
                log_message('info', 'GCS service initialized successfully');
            } catch (\Exception $e) {
                log_message('warning', 'GCS service initialization failed: ' . $e->getMessage());
            }

            // Process local files to GCS
            if ($gcsService) {
                $this->processLocalFilesToGcs($applicant_id, $gcsService);
            } else {
                log_message('warning', 'GCS service not available, skipping file upload to cloud storage');
            }

            // Load models
            $applicantsModel = new \App\Models\ApplicantsModel();
            $applicantFilesModel = new \App\Models\ApplicantFilesModel();
            $applicantEducationModel = new \App\Models\ApplicantEducationModel();
            $applicantExperiencesModel = new \App\Models\ApplicantsExperiencesModel();

            // Collect all applicant data (files should now be in GCS)
            $files = $applicantFilesModel->where('applicant_id', $applicant_id)->findAll();

            // Remove file_extracted_texts from files to prevent JavaScript escape sequence issues
            foreach ($files as &$file) {
                if (isset($file['file_extracted_texts'])) {
                    unset($file['file_extracted_texts']);
                }
            }

            $applicantData = [
                'personal_info' => $applicantsModel->find($applicant_id),
                'files' => $files,
                'education' => $applicantEducationModel->where('applicant_id', $applicant_id)->findAll(),
                'experiences' => $applicantExperiencesModel->where('applicant_id', $applicant_id)->findAll()
            ];

            // Remove sensitive data
            if (isset($applicantData['personal_info']['password'])) {
                unset($applicantData['personal_info']['password']);
            }
            if (isset($applicantData['personal_info']['activation_token'])) {
                unset($applicantData['personal_info']['activation_token']);
            }

            log_message('info', 'STEP 1: Completed - Profile generation with file processing successful');

            return $this->response->setJSON([
                'success' => true,
                'data' => $applicantData
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Process application error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error processing application data: ' . $e->getMessage()
            ]);
        }
    }

    public function apply($positionId)
    {
        try {
            if (!$this->request->isAJAX()) {
                log_message('error', 'Job application: Non-AJAX request received');
                return $this->response->setJSON(['success' => false, 'message' => 'Invalid request method']);
            }

            // Check if position exists and is active
            $position = $this->positionsModel
                ->where('id', $positionId)
                ->where('status', 'active')
                ->first();

            if (!$position) {
                log_message('error', "Job application: Position {$positionId} not found or not active");
                return $this->response->setJSON(['success' => false, 'message' => 'Position not found or not active']);
            }

            // Check if already applied
            $existingApplication = $this->applicationsModel
                ->where('applicant_id', session()->get('applicant_id'))
                ->where('position_id', $positionId)
                ->first();

            if ($existingApplication) {
                log_message('error', "Job application: Duplicate application for position {$positionId} by applicant " . session()->get('applicant_id'));
                return $this->response->setJSON(['success' => false, 'message' => 'You have already applied for this position']);
            }

            // Get applicant details
            $applicantModel = new \App\Models\ApplicantsModel();
            $applicant = $applicantModel->find(session()->get('applicant_id'));

            if (!$applicant) {
                log_message('error', "Job application: Applicant " . session()->get('applicant_id') . " not found");
                return $this->response->setJSON(['success' => false, 'message' => 'Applicant information not found']);
            }

            // Generate unique application number
            $applicationNumber = 'APP' . date('Y') . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);

            // Prepare application data
            $applicationData = [
                'org_id' => $position['org_id'],
                'exercise_id' => $position['exercise_id'],
                'applicant_id' => session()->get('applicant_id'),
                'position_id' => $positionId,
                'application_number' => $applicationNumber,
                'email_address' => $applicant['email'],
                'first_name' => $applicant['first_name'],
                'last_name' => $applicant['last_name'],
                'gender' => $applicant['gender'],
                'date_of_birth' => $applicant['dobirth'],
                'place_of_origin' => $applicant['place_of_origin'],
                'id_photo_path' => $applicant['id_photo_path'],
                'contact_details' => $applicant['contact_details'],
                'location_address' => $applicant['location_address'],
                'id_numbers' => $applicant['id_numbers'],
                'current_employer' => $applicant['current_employer'],
                'current_position' => $applicant['current_position'],
                'current_salary' => $applicant['current_salary'],
                'citizenship' => $applicant['citizenship'],
                'marital_status' => $applicant['marital_status'],
                'date_of_marriage' => $applicant['date_of_marriage'],
                'spouse_employer' => $applicant['spouse_employer'],
                'children' => $applicant['children'],
                'offence_convicted' => $applicant['offence_convicted'],
                'referees' => $applicant['referees'],
                'how_did_you_hear_about_us' => $applicant['how_did_you_hear_about_us'],
                'signature_path' => $applicant['signature_path'],
                'publications' => $applicant['publications'],
                'awards' => $applicant['awards'],
                'application_status' => 'pending',
                'created_by' => session()->get('applicant_id')
            ];

            // Server-side validation: Ensure exercise_id and org_id are present
            if (!$this->validateApplicationData($applicationData, $positionId)) {
                return $this->response->setJSON(['success' => false, 'message' => 'Required application data validation failed']);
            }

            // Start transaction using model
            $this->applicationsModel->db->transStart();

            try {
                // Insert application using model
                if (!$this->applicationsModel->insert($applicationData)) {
                    $error = $this->applicationsModel->errors();
                    log_message('error', 'Job application: Database insert failed - ' . json_encode($error));
                    throw new \Exception('Database error: ' . json_encode($error));
                }

                $applicationId = $this->applicationsModel->getInsertID();

                // Copy experiences data
                $experiences = $this->applicantExperiencesModel->where('applicant_id', session()->get('applicant_id'))->findAll();
                foreach ($experiences as $experience) {
                    $experienceData = [
                        'application_id' => $applicationId,
                        'applicant_id' => session()->get('applicant_id'),
                        'employer' => $experience['employer'],
                        'employer_contacts_address' => $experience['employer_contacts_address'],
                        'position' => $experience['position'],
                        'date_from' => $experience['date_from'],
                        'date_to' => $experience['date_to'],
                        'achievements' => $experience['achievements'],
                        'work_description' => $experience['work_description'],
                        'created_by' => session()->get('applicant_id')
                    ];

                    if (!$this->appxApplicationExperiencesModel->insert($experienceData)) {
                        throw new \Exception('Failed to copy experience data');
                    }
                }

                // Copy education data
                $educationRecords = $this->applicantEducationModel->where('applicant_id', session()->get('applicant_id'))->findAll();
                foreach ($educationRecords as $education) {
                    $educationData = [
                        'application_id' => $applicationId,
                        'applicant_id' => session()->get('applicant_id'),
                        'institution' => $education['institution'],
                        'course' => $education['course'],
                        'date_from' => $education['date_from'],
                        'date_to' => $education['date_to'],
                        'education_level' => $education['education_level'],
                        'units' => $education['units'],
                        'created_by' => session()->get('applicant_id')
                    ];

                    if (!$this->appxApplicationEducationModel->insert($educationData)) {
                        throw new \Exception('Failed to copy education data');
                    }
                }

                // Copy existing files data with GCS support and duplicate prevention
                $existingFiles = $this->applicantFilesModel->where('applicant_id', session()->get('applicant_id'))->findAll();

                foreach ($existingFiles as $file) {
                    // Check if this applicant_file_id already exists in appx_application_files
                    if ($this->appxApplicationFilesModel->applicantFileIdExists($file['id'])) {
                        // File already exists in repository, get it and link to application
                        $existingFile = $this->appxApplicationFilesModel->getExistingFile($file['id']);
                        if ($existingFile && !$this->appxApplicationFilesModel->isFileLinkedToApplication($applicationId, $existingFile['id'])) {
                            $this->appxApplicationFilesModel->linkFileToApplication($applicationId, $existingFile['id']);
                            log_message('info', "Linked existing file: applicant_file_id {$file['id']} to application {$applicationId}");
                        } else {
                            log_message('info', "File already linked: applicant_file_id {$file['id']} to application {$applicationId}");
                        }
                        continue;
                    }

                    // Create new file record in repository
                    $newFileData = [
                        'applicant_id' => session()->get('applicant_id'),
                        'applicant_file_id' => $file['id'],
                        'file_title' => $file['file_title'],
                        'file_description' => $file['file_description'],
                        'extracted_texts' => $file['file_extracted_texts'] ?? null,
                        'created_by' => session()->get('applicant_id')
                    ];

                    // Try GCS upload first (priority), then fallback to local
                    $gcsService = null;
                    try {
                        $gcsService = new \App\Services\GoogleCloudStorageService();
                    } catch (\Exception $e) {
                        log_message('warning', 'GCS not available, will use local storage: ' . $e->getMessage());
                    }

                    if ($gcsService && !empty($file['file_path'])) {
                        // Try to upload to GCS first
                        $originalFilePath = FCPATH . ltrim($file['file_path'], 'public/');
                        if (file_exists($originalFilePath)) {
                            $gcsResult = $gcsService->uploadFile($originalFilePath, 'applications', $applicationId);
                            if ($gcsResult['success']) {
                                $newFileData['gcs_path'] = $gcsResult['gcs_path'];
                                $newFileData['storage_type'] = 'gcs';
                                $newFileData['public_url'] = $gcsResult['public_url'];
                                $newFileData['file_path'] = null;
                                log_message('info', "File uploaded to GCS: {$gcsResult['gcs_path']}");
                            } else {
                                log_message('error', "GCS upload failed: {$gcsResult['error']}. Using local storage.");
                                $newFileData['file_path'] = $file['file_path'];
                                $newFileData['storage_type'] = 'local';
                                $newFileData['gcs_path'] = null;
                                $newFileData['public_url'] = !empty($file['file_path']) ? base_url($file['file_path']) : null;
                            }
                        } else {
                            log_message('warning', "Original file not found: {$originalFilePath}. Using local reference.");
                            $newFileData['file_path'] = $file['file_path'];
                            $newFileData['storage_type'] = 'local';
                            $newFileData['gcs_path'] = null;
                            $newFileData['public_url'] = !empty($file['file_path']) ? base_url($file['file_path']) : null;
                        }
                    } else {
                        // Fallback to local storage
                        $newFileData['file_path'] = $file['file_path'];
                        $newFileData['storage_type'] = 'local';
                        $newFileData['gcs_path'] = null;
                        $newFileData['public_url'] = !empty($file['file_path']) ? base_url($file['file_path']) : null;
                    }

                    $newFileId = $this->appxApplicationFilesModel->insert($newFileData);
                    if ($newFileId) {
                        // Link the new file to this application
                        $this->appxApplicationFilesModel->linkFileToApplication($applicationId, $newFileId);
                        log_message('info', "Created and linked new file: applicant_file_id {$file['id']} to application {$applicationId}");
                    } else {
                        throw new \Exception('Failed to create file record in global repository');
                    }
                }

                // Complete transaction
                $this->applicationsModel->db->transComplete();

                if ($this->applicationsModel->db->transStatus() === false) {
                    throw new \Exception('Transaction failed');
                }

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Application submitted successfully'
                ]);

            } catch (\Exception $e) {
                // Rollback transaction using model
                $this->applicationsModel->db->transRollback();
                log_message('error', 'Job application: Transaction failed - ' . $e->getMessage());

                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Error submitting application: ' . $e->getMessage()
                ]);
            }
        } catch (\Exception $e) {
            log_message('error', 'Job application: Unexpected error - ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'An unexpected error occurred. Please try again later.'
            ]);
        }
    }

    /**
     * Initialize GCS service with retry mechanism
     *
     * @param int $maxRetries Maximum number of retry attempts
     * @param int $retryDelay Delay between retries in seconds
     * @return \App\Services\GoogleCloudStorageService|null
     */
    private function initializeGcsWithRetry(int $maxRetries = 3, int $retryDelay = 2): ?\App\Services\GoogleCloudStorageService
    {
        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            try {
                $gcsService = new \App\Services\GoogleCloudStorageService();
                log_message('info', "GCS service initialized successfully on attempt {$attempt}");
                return $gcsService;
            } catch (\Exception $e) {
                log_message('warning', "GCS initialization attempt {$attempt} failed: " . $e->getMessage());

                if ($attempt < $maxRetries) {
                    log_message('info', "Retrying GCS initialization in {$retryDelay} seconds...");
                    sleep($retryDelay);
                } else {
                    log_message('error', "GCS initialization failed after {$maxRetries} attempts");
                }
            }
        }

        return null;
    }

    /**
     * Retry GCS operation with exponential backoff
     *
     * @param callable $operation The GCS operation to retry
     * @param int $maxRetries Maximum number of retry attempts
     * @return array Operation result
     */
    private function retryGcsOperation(callable $operation, int $maxRetries = 3): array
    {
        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            $result = $operation();

            if ($result['success']) {
                if ($attempt > 1) {
                    log_message('info', "GCS operation succeeded on attempt {$attempt}");
                }
                return $result;
            }

            log_message('warning', "GCS operation attempt {$attempt} failed: " . ($result['error'] ?? 'Unknown error'));

            if ($attempt < $maxRetries) {
                $delay = pow(2, $attempt - 1); // Exponential backoff: 1s, 2s, 4s
                log_message('info', "Retrying GCS operation in {$delay} seconds...");
                sleep($delay);
            }
        }

        log_message('error', "GCS operation failed after {$maxRetries} attempts");
        return $result;
    }

    public function finalSubmission($positionId)
    {
        try {
            $applicant_id = session()->get('applicant_id');

            if (!$applicant_id) {
                return redirect()->to('/applicant/login')->with('error', 'Please login to continue');
            }

            // Get profile data from form
            $profileData = $this->request->getPost('profile_data');

            // Debug logging
            log_message('info', 'Final submission started for position: ' . $positionId);
            log_message('info', 'Profile data received: ' . ($profileData ? 'YES' : 'NO'));
            log_message('info', 'Profile data length: ' . strlen($profileData ?? ''));

            if (!$profileData) {
                log_message('error', 'Profile data is missing in final submission');
                return redirect()->back()->with('error', 'Profile data is required for submission');
            }

            // Check if position exists and is active
            $position = $this->positionsModel
                ->where('id', $positionId)
                ->where('status', 'active')
                ->first();

            if (!$position) {
                return redirect()->back()->with('error', 'Position not found or not active');
            }

            // Check if user has already applied for this position
            $existingApplication = $this->applicationsModel
                ->where('applicant_id', $applicant_id)
                ->where('position_id', $positionId)
                ->first();

            if ($existingApplication) {
                return redirect()->to('/applicant/applications')->with('error', 'You have already applied for this position.');
            }

            // Get exercise and organization details
            $exercise = $this->exercisesModel->find($position['exercise_id']);
            $organization = $this->organizationsModel->find($position['org_id']);

            if (!$exercise || !$organization) {
                return redirect()->back()->with('error', 'Exercise or organization not found');
            }

            // Parse profile data
            $profileDataArray = json_decode($profileData, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                log_message('error', 'JSON decode error: ' . json_last_error_msg());
                return redirect()->back()->with('error', 'Invalid profile data format');
            }

            // Start database transaction
            $this->applicationsModel->db->transStart();

            try {
                // Initialize GCS service
                $gcsService = null;
                try {
                    $gcsService = new \App\Services\GoogleCloudStorageService();
                    log_message('info', 'GCS service initialized successfully');
                } catch (\Exception $e) {
                    log_message('warning', 'GCS service initialization failed: ' . $e->getMessage());
                }

                // STEP 1: Check and copy files from ApplicantFilesModel to AppxApplicationFilesModel
                log_message('info', 'STEP 1: Starting file repository processing');
                $this->processFilesToApplicationRepository($applicant_id, $gcsService);

                // STEP 2: Insert application data into AppxApplicationDetailsModel
                log_message('info', 'STEP 2: Starting application data insertion');
                $applicationId = $this->insertApplicationData($positionId, $applicant_id, $position, $exercise, $organization, $profileDataArray, $profileData);

                // STEP 3: Link all application files to this application
                log_message('info', 'STEP 3: Linking files to application');
                $this->linkFilesToApplication($applicationId, $applicant_id);

                // Complete transaction
                $this->applicationsModel->db->transComplete();

                if ($this->applicationsModel->db->transStatus() === false) {
                    throw new \Exception('Transaction failed');
                }

                // Generate application number for success message
                $application = $this->applicationsModel->find($applicationId);
                $applicationNumber = $application['application_number'];

                log_message('info', "Application submitted successfully: {$applicationNumber}");
                return redirect()->to('/applicant/applications')->with('success', 'Application submitted successfully! Application Number: ' . $applicationNumber);

            } catch (\Exception $e) {
                $this->applicationsModel->db->transRollback();
                log_message('error', 'Final submission error: ' . $e->getMessage());
                return redirect()->back()->with('error', 'Error submitting application: ' . $e->getMessage());
            }

        } catch (\Exception $e) {
            log_message('error', 'Final submission unexpected error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'An unexpected error occurred. Please try again later.');
        }
    }

    /**
     * STEP 1: Process local files to GCS and delete local copies
     */
    private function processLocalFilesToGcs($applicant_id, $gcsService)
    {
        if (!$gcsService) {
            log_message('error', 'GCS service not available for file processing');
            throw new \Exception('File processing service unavailable. Please try again later.');
        }

        $applicantFiles = $this->applicantFilesModel->where('applicant_id', $applicant_id)->findAll();
        log_message('info', 'Found ' . count($applicantFiles) . ' applicant files to process');

        foreach ($applicantFiles as $index => $file) {
            // If file already has a public_url (in GCS), remove any lingering local copy and clear file_path
            if (!empty($file['public_url']) && !empty($file['file_path'])) {
                $storedPath = str_replace('\\', '/', (string)$file['file_path']);
                $relativeUnderPublic = (strpos($storedPath, 'public/') === 0)
                    ? substr($storedPath, 7)
                    : ltrim($storedPath, '/');
                $originalFilePath = rtrim(FCPATH, '/\\') . DIRECTORY_SEPARATOR . str_replace('/', DIRECTORY_SEPARATOR, $relativeUnderPublic);

                if (is_file($originalFilePath) && file_exists($originalFilePath)) {
                    if (@unlink($originalFilePath)) {
                        log_message('info', "STEP 1: Deleted lingering local file (already in GCS): {$originalFilePath}");
                    } else {
                        log_message('warning', "STEP 1: Failed to delete lingering local file: {$originalFilePath}");
                    }
                } else {
                    log_message('info', "STEP 1: No local file found to delete for record {$file['id']} (already in GCS)");
                }

                // Clear local file path and ensure storage_type reflects GCS
                $updateFields = [
                    'file_path'  => '',
                    'updated_by' => $applicant_id
                ];
                if ((string)($file['storage_type'] ?? '') !== 'gcs') {
                    $updateFields['storage_type'] = 'gcs';
                }
                $this->applicantFilesModel->update($file['id'], $updateFields);
                log_message('info', "STEP 1: Cleared file_path for record {$file['id']} (already in GCS)");
            }

            // Only process files explicitly marked as local
            $isLocal = isset($file['storage_type']) && strtolower((string)$file['storage_type']) === 'local';
            if ($isLocal && !empty($file['file_path'])) {
                // File is local and not yet uploaded to GCS - upload it now
                // Resolve absolute local file path safely from stored DB path
                $storedPath = str_replace('\\', '/', (string)$file['file_path']);
                $relativeUnderPublic = (strpos($storedPath, 'public/') === 0)
                    ? substr($storedPath, 7)
                    : ltrim($storedPath, '/');
                $originalFilePath = rtrim(FCPATH, '/\\') . DIRECTORY_SEPARATOR . str_replace('/', DIRECTORY_SEPARATOR, $relativeUnderPublic);

                log_message('info', "STEP 1: Resolving local file path | stored='{$file['file_path']}' | resolved='{$originalFilePath}'");

                if (is_file($originalFilePath) && file_exists($originalFilePath)) {
                    log_message('info', "STEP 1: Uploading local file to GCS: {$file['file_title']}");

                    // Upload to GCS with retry mechanism
                    $gcsResult = $this->retryGcsOperation(function() use ($gcsService, $originalFilePath, $applicant_id) {
                        return $gcsService->uploadFromPath($originalFilePath, 'applicants', $applicant_id);
                    });

                    if ($gcsResult['success']) {
                        // Update ApplicantFilesModel record with GCS info
                        $updateData = [
                            'gcs_path' => $gcsResult['gcs_path'],
                            'storage_type' => 'gcs',
                            'public_url' => $gcsResult['public_url'],
                            'updated_by' => $applicant_id
                        ];

                        if ($this->applicantFilesModel->update($file['id'], $updateData)) {
                            log_message('info', "STEP 1: Updated ApplicantFilesModel record {$file['id']} with GCS info");

                            // Delete local file after successful GCS upload
                            if (unlink($originalFilePath)) {
                                log_message('info', "STEP 1: Deleted local file: {$originalFilePath}");
                                // Clear local file path in DB only after deletion succeeds
                                $this->applicantFilesModel->update($file['id'], [
                                    'file_path' => '',
                                    'updated_by' => $applicant_id
                                ]);
                                log_message('info', "STEP 1: Cleared local file_path for record {$file['id']}");
                            } else {
                                log_message('warning', "STEP 1: Failed to delete local file: {$originalFilePath}");
                            }
                        } else {
                            log_message('error', "STEP 1: Failed to update ApplicantFilesModel record {$file['id']} with GCS info");
                            throw new \Exception("Failed to update applicant file record {$file['id']} with GCS information");
                        }
                    } else {
                        log_message('error', "STEP 1: Failed to upload file to GCS: {$gcsResult['error']}");
                        throw new \Exception("Failed to upload applicant file {$file['id']} to GCS: {$gcsResult['error']}");
                    }
                } else {
                    log_message('warning', "STEP 1: Local file not found: {$originalFilePath}");
                }
            }
        }

        log_message('info', 'STEP 1: Completed - All local files uploaded to GCS and local copies deleted');
    }

    /**
     * STEP 2: Process files from ApplicantFilesModel to AppxApplicationFilesModel
     */
    private function processFilesToApplicationRepository($applicant_id, $gcsService)
    {
        $applicantFiles = $this->applicantFilesModel->where('applicant_id', $applicant_id)->findAll();

        foreach ($applicantFiles as $file) {
            // Check if this file already exists in the application repository
            $existingFile = $this->appxApplicationFilesModel->getExistingFile($file['id']);

            if ($existingFile) {
                log_message('info', "STEP 2: File already exists in repository: applicant_file_id {$file['id']}");
                continue;
            }

            // Create new file record in repository by copying from ApplicantFilesModel
            $newFileData = [
                'applicant_id' => $applicant_id,
                'applicant_file_id' => $file['id'],
                'file_title' => $file['file_title'],
                'file_description' => $file['file_description'],
                'extracted_texts' => $file['file_extracted_texts'] ?? null,
                'created_by' => $applicant_id
            ];

            // If file is in GCS, copy it to create a new instance for the application
            if ($gcsService && !empty($file['gcs_path']) && $file['storage_type'] === 'gcs') {
                log_message('info', "STEP 2: Copying GCS file for application repository: {$file['file_title']}");

                $copyResult = $this->retryGcsOperation(function() use ($gcsService, $file, $applicant_id) {
                    return $gcsService->copyFile($file['gcs_path'], 'applications', $applicant_id);
                });

                if ($copyResult['success']) {
                    $newFileData['gcs_path'] = $copyResult['gcs_path'];
                    $newFileData['storage_type'] = 'gcs';
                    $newFileData['public_url'] = $copyResult['public_url'];
                    $newFileData['file_path'] = 'public/' . $copyResult['gcs_path']; // Add public/ prefix as per requirements
                    log_message('info', "STEP 2: File copied to GCS: {$copyResult['gcs_path']}");
                } else {
                    log_message('error', "STEP 2: Failed to copy file to GCS: {$copyResult['error']}");
                    // Fallback to local storage reference (ensure single public/ prefix)
                    $originalPath = str_replace('\\', '/', (string)$file['file_path']);
                    if (strpos($originalPath, 'public/') !== 0) {
                        $originalPath = 'public/' . ltrim($originalPath, '/');
                    }
                    $newFileData['file_path'] = $originalPath;
                    $newFileData['storage_type'] = 'local';
                }
            } else {
                // Use local storage reference (ensure single public/ prefix)
                $originalPath = str_replace('\\', '/', (string)$file['file_path']);
                if (strpos($originalPath, 'public/') !== 0) {
                    $originalPath = 'public/' . ltrim($originalPath, '/');
                }
                $newFileData['file_path'] = $originalPath;
                $newFileData['storage_type'] = 'local';
            }

            // Insert new file record
            $newFileId = $this->appxApplicationFilesModel->insert($newFileData);
            if ($newFileId) {
                log_message('info', "STEP 2: Created new file record in repository: applicant_file_id {$file['id']}");
            } else {
                throw new \Exception('Failed to create file record in application repository');
            }
        }

        log_message('info', 'STEP 2: Completed - All files processed to application repository');
    }



    /**
     * STEP 3: Insert application data into AppxApplicationDetailsModel
     */
    private function insertApplicationData($positionId, $applicant_id, $position, $exercise, $organization, $profileDataArray, $profileData)
    {
        // Generate application number
        $applicationNumber = $this->generateApplicationNumber($organization['org_code'], $exercise['id']);

        // Prepare application data
        $applicationData = [
            'org_id' => $position['org_id'],
            'exercise_id' => $position['exercise_id'],
            'applicant_id' => $applicant_id,
            'position_id' => $positionId,
            'application_number' => $applicationNumber,
            'email_address' => $profileDataArray['personal_information']['email'] ?? '',
            'first_name' => $profileDataArray['personal_information']['first_name'] ?? '',
            'last_name' => $profileDataArray['personal_information']['last_name'] ?? '',
            'gender' => $profileDataArray['personal_information']['gender'] ?? '',
            'date_of_birth' => $profileDataArray['personal_information']['date_of_birth'] ?? null,
            'place_of_origin' => $profileDataArray['personal_information']['place_of_origin'] ?? '',
            'contact_details' => json_encode($profileDataArray['personal_information']['contact_details'] ?? []),
            'location_address' => json_encode($profileDataArray['personal_information']['location_address'] ?? []),
            'id_numbers' => json_encode($profileDataArray['personal_information']['id_numbers'] ?? []),
            'current_employer' => $profileDataArray['personal_information']['current_employer'] ?? '',
            'current_position' => $profileDataArray['personal_information']['current_position'] ?? '',
            'current_salary' => $profileDataArray['personal_information']['current_salary'] ?? '',
            'citizenship' => $profileDataArray['personal_information']['citizenship'] ?? '',
            'marital_status' => $profileDataArray['personal_information']['marital_status'] ?? '',
            'date_of_marriage' => $profileDataArray['personal_information']['date_of_marriage'] ?? null,
            'spouse_employer' => $profileDataArray['personal_information']['spouse_employer'] ?? '',
            'is_public_servant' => $profileDataArray['personal_information']['is_public_servant'] ?? 0,
            'public_service_file_number' => $profileDataArray['personal_information']['public_service_file_number'] ?? '',
            'employee_of_org_id' => $profileDataArray['personal_information']['employee_of_org_id'] ?? null,
            'children' => json_encode($profileDataArray['personal_information']['children'] ?? []),
            'offence_convicted' => $profileDataArray['personal_information']['offence_convicted'] ?? 0,
            'referees' => json_encode($profileDataArray['referees'] ?? []),
            'how_did_you_hear_about_us' => $profileDataArray['personal_information']['how_did_you_hear_about_us'] ?? '',
            'publications' => json_encode($profileDataArray['publications'] ?? []),
            'awards' => json_encode($profileDataArray['awards'] ?? []),
            'profile_details' => $profileData,
            'application_status' => 'submitted',
            'created_by' => $applicant_id
        ];

        // Insert application data
        if (!$this->applicationsModel->insert($applicationData)) {
            $error = $this->applicationsModel->errors();
            log_message('error', 'STEP 3: Application insert failed: ' . json_encode($error));
            throw new \Exception('Failed to insert application data: ' . json_encode($error));
        }

        $applicationId = $this->applicationsModel->getInsertID();
        log_message('info', "STEP 3: Application created with ID: {$applicationId}");

        return $applicationId;
    }

    /**
     * Link all application files to the application
     */
    private function linkFilesToApplication($applicationId, $applicant_id)
    {
        $applicationFiles = $this->appxApplicationFilesModel->where('applicant_id', $applicant_id)->findAll();
        foreach ($applicationFiles as $appFile) {
            if (!$this->appxApplicationFilesModel->isFileLinkedToApplication($applicationId, $appFile['id'])) {
                $this->appxApplicationFilesModel->linkFileToApplication($applicationId, $appFile['id']);
                log_message('info', "Linked file {$appFile['id']} to application {$applicationId}");
            }
        }
    }

    /**
     * Generate unique application number
     */
    private function generateApplicationNumber($orgCode, $exerciseId)
    {
        // Generate application number format: ORG-YEAR-EXERCISE-RANDOM
        $year = date('Y');
        $random = str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);
        return strtoupper($orgCode) . '-' . $year . '-' . $exerciseId . '-' . $random;
    }

    /**
     * Download application file
     *
     * @param string $filename
     * @return mixed
     */
    public function downloadFile($filename)
    {
        // Security check: Verify the file belongs to this applicant
        $file = $this->appxApplicationFilesModel
            ->where('applicant_id', session()->get('applicant_id'))
            ->where('file_path', 'public/uploads/applications/' . $filename)
            ->first();

        if (!$file) {
            return $this->response->setStatusCode(403)->setBody('Access denied');
        }

        $path = FCPATH . 'public/uploads/applications/' . $filename;

        if (!file_exists($path)) {
            return $this->response->setStatusCode(404)->setBody('File not found');
        }

        return $this->response->download($path, null)->setFileName($filename);
    }

    /**
     * Display list of applications for the logged-in applicant
     */
    public function applications()
    {
        // Get applicant ID from session
        $applicant_id = session()->get('applicant_id');

        // Debug logging
        log_message('info', "Applications page - Applicant ID from session: " . ($applicant_id ?? 'NULL'));

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to view your applications');
        }

        // Get real applications with related data
        $applicationData = [];
        $applications = $this->applicationsModel->select('
                appx_application_details.*,
                positions.designation,
                positions.location as position_location,
                positions.annual_salary,
                positions.classification,
                dakoii_org.org_name,
                dakoii_org.org_code,
                dakoii_org.location_lock_province,
                exercises.exercise_name,
                exercises.advertisement_no
            ')
            ->join('positions', 'appx_application_details.position_id = positions.id', 'left')
            ->join('dakoii_org', 'appx_application_details.org_id = dakoii_org.id', 'left')
            ->join('exercises', 'appx_application_details.exercise_id = exercises.id', 'left')
            ->where('appx_application_details.applicant_id', $applicant_id)
            ->orderBy('appx_application_details.created_at', 'DESC')
            ->findAll();

        // Debug logging
        log_message('info', "Applications query result count: " . count($applications));
        if (!empty($applications)) {
            log_message('info', "First application: " . json_encode($applications[0]));
        }

        // Format data to match the expected structure in the view
        foreach ($applications as $app) {
            $applicationData[] = [
                'application' => [
                    'id' => $app['id'],
                    'application_number' => $app['application_number'],
                    'application_status' => $app['application_status'] ?? 'pending',
                    'created_at' => $app['created_at'],
                    'updated_at' => $app['updated_at']
                ],
                'position' => [
                    'id' => $app['position_id'],
                    'designation' => $app['designation'] ?? 'Position Not Found',
                    'department' => $app['org_name'] ?? 'Department Not Found',
                    'salary_range' => $app['annual_salary'] ? $app['annual_salary'] : 'Not Specified',
                    'location' => $app['position_location'] ?? 'Location Not Specified',
                    'classification' => $app['classification'] ?? ''
                ],
                'organization' => [
                    'id' => $app['org_id'],
                    'org_name' => $app['org_name'] ?? 'Organization Not Found',
                    'org_code' => $app['org_code'] ?? '',
                    'location_lock_province' => $app['location_lock_province'] ?? ''
                ],
                'exercise' => [
                    'id' => $app['exercise_id'],
                    'exercise_name' => $app['exercise_name'] ?? 'Exercise Not Found',
                    'advertisement_no' => $app['advertisement_no'] ?? ''
                ]
            ];
        }

        return view('applicant/applicant_applications', [
            'title' => 'My Applications',
            'menu' => 'applications',
            'applications' => $applicationData
        ]);
    }

    /**
     * Copy file locally (fallback method)
     *
     * @param array $file Original file data
     * @param int $applicationId Application ID
     * @param string $finalApplicationFilesDir Destination directory
     * @param array $fileData File data array to update
     * @return array Updated file data
     */
    private function copyFileLocally(array $file, int $applicationId, string $finalApplicationFilesDir, array $fileData): array
    {
        $newFilePath = null;

        if (!empty($file['file_path'])) {
            // Fix the file path - remove 'public/' prefix if it exists for FCPATH
            $cleanFilePath = $file['file_path'];
            if (strpos($cleanFilePath, 'public/') === 0) {
                $cleanFilePath = substr($cleanFilePath, 7); // Remove 'public/' prefix
            }

            $originalFilePath = FCPATH . $cleanFilePath;

            log_message('info', 'Attempting to copy file locally from: ' . $originalFilePath);

            if (file_exists($originalFilePath)) {
                $fileInfo = pathinfo($originalFilePath);
                $newFileName = 'file_' . $applicationId . '_' . $file['id'] . '_' . time() . '.' . $fileInfo['extension'];
                $newFileFullPath = $finalApplicationFilesDir . '/' . $newFileName;

                if (copy($originalFilePath, $newFileFullPath)) {
                    $newFilePath = 'public/uploads/applications/' . $applicationId . '/' . $newFileName;
                    log_message('info', 'File copied locally: ' . $originalFilePath . ' -> ' . $newFileFullPath);
                } else {
                    log_message('error', 'Failed to copy file locally: ' . $originalFilePath . ' -> ' . $newFileFullPath);
                }
            } else {
                log_message('warning', 'Original file not found for local copy: ' . $originalFilePath);
            }
        }

        $fileData['file_path'] = $newFilePath;
        $fileData['storage_type'] = 'local';
        $fileData['gcs_path'] = null;
        $fileData['public_url'] = $newFilePath ? base_url($newFilePath) : null;

        return $fileData;
    }
}